# Wizlop Design & Responsiveness Rules

## 🎨 COLOR SYSTEM
- **ONLY use colors from `application/app/colors.ts`**
- Never hardcode colors like `#FF0000`, `blue-500`, etc.
- Use `colors.brand.*`, `colors.neutral.*`, `colors.ui.*`, `colors.utility.*`
- For gradients, use `gradients.primary`, `gradients.secondary`, etc.

## 📱 RESPONSIVE BREAKPOINTS
- **Mobile**: 320px - 767px (sm and below)
- **Tablet**: 768px - 1023px (md)
- **Desktop**: 1024px+ (lg and above)

## 🔘 BUTTON STANDARDIZATION
### Primary Buttons
```
className: 'px-6 py-3 rounded-xl font-medium transition-all duration-200'
background: gradients.primary or colors.brand.blue
color: white
```

### Secondary Buttons
```
className: 'px-6 py-3 rounded-xl font-medium transition-all duration-200'
background: colors.ui.gray100
color: colors.neutral.textBlack
border: colors.ui.gray200
```

### Small Buttons (filters, toggles)
```
className: 'px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200'
```

## 📐 LAYOUT SYSTEM
### Container Widths
- **Mobile**: `w-full px-4` (16px padding)
- **Tablet**: `w-full px-6` (24px padding)
- **Desktop**: `max-w-7xl mx-auto px-12` (48px padding, centered)

### Section Spacing
- **Mobile**: `py-8` (32px vertical)
- **Tablet**: `py-12` (48px vertical)
- **Desktop**: `py-16` (64px vertical)

## 📏 VIEWPORT HEIGHT RULES
### Full Screen Pages (Hero, Landing)
```
className: 'min-h-screen'
style: { minHeight: '100vh' }
```

### Content Pages (POI, Profile)
```
className: 'min-h-screen'
Use page scroll, not container scroll
```

### Modal/Overlay Content
```
maxHeight: 'calc(100vh - 100px)'
overflow-y: auto
```

## 🔤 TYPOGRAPHY SCALE
### Headers
- **H1**: `text-2xl md:text-3xl lg:text-4xl font-black`
- **H2**: `text-xl md:text-2xl lg:text-3xl font-bold`
- **H3**: `text-lg md:text-xl lg:text-2xl font-semibold`

### Body Text
- **Large**: `text-base md:text-lg`
- **Normal**: `text-sm md:text-base`
- **Small**: `text-xs md:text-sm`

## 🎯 COMPONENT SPACING
### Cards
- **Padding**: `p-4 md:p-6 lg:p-8`
- **Gap**: `gap-4 md:gap-6 lg:gap-8`

### Forms
- **Input Height**: `py-2 md:py-3`
- **Input Padding**: `px-3 md:px-4`
- **Form Gap**: `gap-4 md:gap-6`

## 🔄 RESPONSIVE PATTERNS
### Flex Layouts
```
Mobile: flex-col
Tablet+: flex-row
Gap: gap-4 md:gap-6 lg:gap-8
```

### Grid Layouts
```
Mobile: grid-cols-1
Tablet: grid-cols-2
Desktop: grid-cols-3 lg:grid-cols-4
```

### Navigation
```
Mobile: Hidden menu with hamburger
Tablet+: Horizontal navigation
```

## 🎨 BACKGROUND PATTERNS
### Page Backgrounds
```
background: Single gradient across entire page
No separate header/body backgrounds
Use colors.neutral.cloudWhite as base
```

### Card Backgrounds
```
background: rgba(255, 255, 255, 0.9)
backdrop-filter: blur(sm)
border: colors.ui.gray200
```

## 📋 LIST & TABLE RULES
### List Views
- Integrate with page scroll (no separate container scroll)
- Sticky headers: `sticky top-0 z-10`
- Row hover: `hover:bg-gray-50`

### Filter Layouts
- Mobile: Stack vertically
- Desktop: Single row with flex-wrap
- No horizontal scroll

## 🚀 PERFORMANCE RULES
- Use `transition-all duration-200` for smooth interactions
- Implement lazy loading for images
- Use `backdrop-blur-sm` for glass effects
- Minimize re-renders with proper React patterns

## 📱 MOBILE-FIRST APPROACH
1. Design for mobile first
2. Enhance for tablet
3. Optimize for desktop
4. Test on actual devices
5. Consider touch targets (min 44px)

## 🎪 ANIMATION GUIDELINES
- Subtle hover effects: `hover:scale-105 hover:shadow-lg`
- Loading states: Use consistent spinners
- Page transitions: Fade in with `opacity` and `transform`
- Keep animations under 300ms for responsiveness