
> wizlop@0.1.0 build:webpack
> webpack --config config/webpack/webpack.dev.js

asset bundle.js 4.51 MiB [emitted] [big] (name: main)
runtime modules 1.04 KiB 5 modules
modules by path ./node_modules/ 1.48 MiB 109 modules
modules by path ./app/ 126 KiB
  modules by path ./app/landing/components/ 92.3 KiB 13 modules
  modules by path ./app/landing/utils/*.ts 13.3 KiB
    ./app/landing/utils/scrollAnimations.ts 4.46 KiB [built] [code generated]
    ./app/landing/utils/responsiveUtils.ts 8.86 KiB [built] [code generated]
  ./app/page.tsx 1.13 KiB [built] [code generated]
  ./app/colors.ts 3.55 KiB [built] [code generated]
  ./app/shared/poi/constants.ts 15.3 KiB [built] [code generated]
webpack 5.100.1 compiled successfully in 3790 ms
