
> wizlop@0.1.0 build:webpack
> webpack --config config/webpack/webpack.dev.js

asset bundle.js 4.58 MiB [compared for emit] [big] (name: main)
runtime modules 1.04 KiB 5 modules
modules by path ./node_modules/ 1.48 MiB 109 modules
modules by path ./app/ 146 KiB
  modules by path ./app/landing/components/ 112 KiB 14 modules
  modules by path ./app/landing/utils/*.ts 13.3 KiB
    ./app/landing/utils/scrollAnimations.ts 4.46 KiB [built] [code generated]
    ./app/landing/utils/responsiveUtils.ts 8.86 KiB [built] [code generated]
  ./app/page.tsx 1.13 KiB [built] [code generated]
  ./app/colors.ts 3.55 KiB [built] [code generated]
  ./app/shared/poi/constants.ts 15.9 KiB [built] [code generated]

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/chat/components/top-bar/TopBar.tsx
165:6-20
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/chat/components/top-bar/TopBar.tsx(165,7)
      TS2304: Cannot find name 'FaMapMarkerAlt'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/chat/components/top-bar/TopBar.tsx(165,7)
      TS2304: Cannot find name 'FaMapMarkerAlt'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

webpack 5.100.1 compiled with 1 error in 3648 ms
