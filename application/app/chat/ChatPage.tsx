/** @format */

'use client';

import {
	InputArea,
	LeftSidebar,
	MessageArea,
	RightSidebar,
	TopBar,
} from 'app/chat/components';
import { ChatContextValue } from 'app/chat/types';
import {
	useChatMessages,
	useChatSessions,
	useChatState,
	useChatUI,
} from 'app/chat/useChatHooks';
import {
	LocationSetup,
	useLocationManager,
	useLocationSetup,
} from 'app/shared/locationManager';
import { useSession } from 'next-auth/react';
import React, { createContext, useContext, useMemo } from 'react';

// ===== CONTEXT =====

const ChatContext = createContext<ChatContextValue | null>(null);

export function useChatContext() {
	const context = useContext(ChatContext);
	if (!context) {
		throw new Error('useChatContext must be used within ChatPageProvider');
	}
	return context;
}

// ===== MAIN CHAT LAYOUT COMPONENT =====

const ChatLayout: React.FC = () => {
	const {
		chatState,
		uiState,
		locationState,
		actions,
		inputRef,
		messagesEndRef,
		messagesContainerRef,
		setUIState,
		showLocationSetup,
		handleLocationSetupComplete,
	} = useChatContext();

	// Memoize extractedLocations to avoid unnecessary re-renders
	const memoizedExtractedLocations = useMemo(
		() => chatState.allExtractedLocations || [],
		[chatState.allExtractedLocations]
	);

	// Debug: Log sidebar open states on each render
	console.log('ChatLayout render:', {
		isLeftOpen: uiState.isLeftOpen,
		isRightOpen: uiState.isRightOpen,
	});

	return (
		<>
			{/* Location Setup Modal */}
			<LocationSetup
				isOpen={showLocationSetup}
				onComplete={handleLocationSetupComplete}
				pageContext='chat'
				isModal={true}
				title='Location Setup Required'
				subtitle='To provide better location-based responses, we need to know your location or you can set it manually.'
			/>

			{/* Main Chat Interface */}
			<div
				className='h-screen w-full overflow-hidden flex flex-row relative'
				style={{
					isolation: 'isolate',
					background: `linear-gradient(180deg,
						rgba(1, 3, 79, 0.1) 0%,
						rgba(163, 247, 181, 0.1) 25%,
						rgba(128, 237, 153, 0.1) 50%,
						rgba(102, 208, 255, 0.1) 75%,
						rgba(51, 194, 255, 0.1) 100%
					), #FEFEFE`,
				}}>
				{/* Left Sidebar */}
				<LeftSidebar
					isOpen={uiState.isLeftOpen}
					onClose={() => setUIState((prev) => ({ ...prev, isLeftOpen: false }))}
					sessionList={chatState.sessionList}
					activeSessionId={chatState.sessionId}
					dropdownIndex={uiState.dropdownIndex}
					onStartNewChat={actions.startNewChat}
					onLoadOldMessages={actions.loadOldMessages}
					onToggleDropdown={(index) =>
						setUIState((prev) => ({ ...prev, dropdownIndex: index }))
					}
					onDeleteSession={actions.handleDeleteSession}
				/>

				{/* Main Content Column */}
				<div className='flex-1 flex flex-col min-w-0'>
					{/* Top Bar */}
					<TopBar
						isLeftOpen={uiState.isLeftOpen}
						isRightOpen={uiState.isRightOpen}
						setIsLeftOpen={(open) => {
							console.log('setIsLeftOpen called with:', open);
							setUIState((prev) => ({ ...prev, isLeftOpen: open }));
						}}
						setIsRightOpen={(open) => {
							console.log('setIsRightOpen called with:', open);
							setUIState((prev) => ({ ...prev, isRightOpen: open }));
						}}
						startNewChat={actions.startNewChat}
						userLocation={locationState.userLocation}
						locationError={locationState.locationError}
						locationLoading={locationState.locationLoading}
						requestAutoLocation={locationState.requestAutoLocation}
					/>

					{/* Message Area */}
					<MessageArea
						messages={chatState.messages}
						showScrollButton={uiState.showScrollButton}
						scrollToBottom={actions.scrollToBottom}
						messagesContainerRef={messagesContainerRef}
						messagesEndRef={messagesEndRef}
						onShowMap={() =>
							setUIState((prev) => ({ ...prev, isRightOpen: true }))
						}
						extractedLocations={chatState.topCandidates}
					/>

					{/* Input Area */}
					<InputArea
						newMessage={chatState.newMessage}
						handleInputChange={actions.handleInputChange}
						handleKeyDown={actions.handleKeyDown}
						inputRef={inputRef}
						onGlobeTransition={actions.handleGlobeTransition}
						isGlobeTransitioning={uiState.isGlobeTransitioning}
						isSending={chatState.isSending}
						hasMessages={chatState.messages.length > 0}
					/>
				</div>

				{/* Right Sidebar */}
				<RightSidebar
					isOpen={uiState.isRightOpen}
					onClose={() =>
						setUIState((prev) => ({ ...prev, isRightOpen: false }))
					}
					userLocation={locationState.userLocation}
					extractedLocations={memoizedExtractedLocations}
				/>
			</div>
		</>
	);
};

// ===== MAIN PROVIDER COMPONENT =====

interface ChatPageProviderProps {
	children: React.ReactNode;
}

export function ChatPageProvider({ children }: ChatPageProviderProps) {
	// Core state hooks (single chatState object)
	const {
		chatState,
		setChatState,
		setMessages,
		setNewMessage,
		setSessionId,
		setSessionList,
		setTopCandidates,
		setAllExtractedLocations,
		addExtractedLocations,
		setIsSending,
	} = useChatState();

	const {
		uiState,
		setUIState,
		setIsLeftOpen,
		setIsRightOpen,
		setDropdownIndex,
		setShowScrollButton,
		setIsGlobeTransitioning,
		toggleDropdown,
		handleGlobeTransition,
	} = useChatUI();

	const { data: session } = useSession();

	// Add setters to uiState for easier access
	const enhancedUIState = {
		...uiState,
		setIsLeftOpen,
		setIsRightOpen,
		setDropdownIndex,
		setShowScrollButton,
		setIsGlobeTransitioning,
	};

	// Use location manager and setup hooks
	const {
		location: userLocation,
		error: locationError,
		isLoading: locationLoading,
		requestAutoLocation,
		markSetupComplete,
		needsLocationSetup,
	} = useLocationManager();

	const { showLocationSetup, handleLocationSetupComplete } = useLocationSetup();

	const locationState = {
		userLocation: userLocation
			? { lat: userLocation.latitude, lng: userLocation.longitude }
			: null,
		locationError,
		locationLoading,
		requestAutoLocation,
		markSetupComplete,
		needsLocationSetup,
		isInitialized: true,
	};

	// Load session history (for left sidebar)
	useChatSessions({
		sessionList: chatState.sessionList,
		sessionId: chatState.sessionId,
		isClient: uiState.isClient,
		setSessionList,
	});

	// Auto-scroll, input focus, scroll-to-bottom button
	const {
		inputRef,
		messagesEndRef,
		messagesContainerRef,
		scrollToBottom,
		handleInputChange: autoResizeInputChange,
	} = useChatMessages({
		messages: chatState.messages,
		isSending: chatState.isSending,
		setShowScrollButton,
	});

	// Input change handler: only update newMessage
	const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		setNewMessage(e.target.value);
		autoResizeInputChange();
	};

	const startNewChat = async () => {
		setMessages([]);
		setNewMessage('');
		setSessionId(null);
	};

	// When sending a message or loading a session, update allExtractedLocations
	const loadOldMessages = async (sessionId: string) => {
		const userId = session?.user?.id;
		if (!userId) {
			console.error('User ID is required to load messages');
			return;
		}
		try {
			setSessionId(sessionId);
			setMessages([]);
			setTopCandidates(null);
			setAllExtractedLocations([]);
			const response = await fetch('/api/chat/messages', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ userId, sessionId }),
			});
			if (!response.ok) {
				throw new Error('Failed to load messages for session');
			}
			const data = await response.json();
			const loadedMessages = data.messages.map(
				(msg: { content: string; role: string }) => ({
					text: msg.content,
					isUser: msg.role === 'user',
					isLoading: false,
				})
			);
			setMessages(loadedMessages);
			// Extract ALL POIs from ALL assistant messages in the conversation
			const allExtractedPOIs: Array<
				Record<string, unknown> & { messageIndex: number }
			> = [];
			data.messages.forEach(
				(
					msg: {
						role: string;
						metadata?: { top_candidates?: Array<Record<string, unknown>> };
					},
					index: number
				) => {
					if (msg.role === 'assistant' && msg.metadata?.top_candidates) {
						const poisWithIndex = msg.metadata.top_candidates.map(
							(poi: Record<string, unknown>) => ({
								...poi,
								messageIndex: index,
							})
						);
						allExtractedPOIs.push(...poisWithIndex);
					}
				}
			);
			setAllExtractedLocations(allExtractedPOIs);
			// Set the most recent topCandidates for compatibility
			const lastAssistantMessage = [...data.messages]
				.reverse()
				.find(
					(msg: {
						role: string;
						metadata?: { top_candidates?: Array<Record<string, unknown>> };
					}) => msg.role === 'assistant' && msg.metadata?.top_candidates
				);
			if (lastAssistantMessage?.metadata?.top_candidates) {
				setTopCandidates(lastAssistantMessage.metadata.top_candidates);
			} else {
				setTopCandidates(null);
			}
		} catch (error) {
			console.error('Error loading old messages:', error);
		}
	};

	const handleDeleteSession = async (sessionId: string) => {
		// TODO: Implement session deletion
		console.log('Deleting session:', sessionId);
	};

	// --- Full sendMessage logic ported from old useChatActions ---
	const sendMessage = async (message: string) => {
		const userId = session?.user?.id;
		if (!message.trim() || !userId) {
			console.log('❌ Message validation failed:', {
				messageEmpty: !message.trim(),
				noUserId: !userId,
			});
			return;
		}
		const userMessage = { text: message, isUser: true };
		const loadingMessage = { text: '', isUser: false, isLoading: true };
		setMessages((prev) => [...prev, userMessage, loadingMessage]);
		setNewMessage('');
		setIsSending(true);
		// --- Session creation logic ---
		const createSessionIfNeeded = async () => {
			if (chatState.sessionId) {
				return chatState.sessionId;
			}
			try {
				const res = await fetch('/api/chat/session', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({ userId }),
				});
				if (!res.ok) {
					let errorMessage = 'Session could not be created.';
					try {
						const errorData = await res.json();
						errorMessage =
							errorData.error?.message || errorData.error || errorMessage;
					} catch {
						errorMessage = `${errorMessage} (${res.status}: ${res.statusText})`;
					}
					throw new Error(errorMessage);
				}
				const data = await res.json();
				const newId = data.sessionId;
				setSessionId(newId);
				setSessionList((prev) => {
					const sessionExists = prev.some((session) => session.id === newId);
					if (!sessionExists) {
						return [{ id: newId, title: '' }, ...prev];
					}
					return prev;
				});
				return newId;
			} catch (err) {
				console.error('❌ Session creation failed', err);
				return null;
			}
		};

		try {
			const currentSessionId = await createSessionIfNeeded();
			if (!currentSessionId) {
				setMessages((prev) => {
					const updated = [...prev];
					updated[updated.length - 1] = {
						text: '❌ Failed to create session.',
						isUser: false,
						isLoading: false,
					};
					return updated;
				});
				setIsSending(false);
				return;
			}

			const payload = {
				message,
				sessionId: currentSessionId,
				userId,
				userLat: userLocation?.latitude || null,
				userLng: userLocation?.longitude || null,
			};

			// Create AbortController with 2.5 minute timeout
			const controller = new AbortController();
			const timeoutId = setTimeout(() => {
				controller.abort();
			}, 150000);

			try {
				const response = await fetch('/api/chat/message', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify(payload),
					signal: controller.signal,
				});
				clearTimeout(timeoutId);
				if (!response.ok) {
					let errorMessage = 'Message could not be sent.';
					try {
						const errorData = await response.json();

						// Handle credit-specific errors
						if (
							response.status === 402 &&
							errorData.code === 'INSUFFICIENT_CREDITS'
						) {
							errorMessage = `💳 ${errorData.error} Visit the Credits page to purchase more credits or earn them by contributing to the platform.`;
						} else {
							errorMessage =
								errorData.error?.message || errorData.error || errorMessage;
						}
					} catch {
						errorMessage = `${errorMessage} (${response.status}: ${response.statusText})`;
					}
					throw new Error(errorMessage);
				}
				const data = await response.json();
				let assistantMessageIndex = 0;
				setMessages((prev) => {
					const newMessages = [...prev];
					assistantMessageIndex = newMessages.length - 1;
					newMessages[assistantMessageIndex] = {
						text: data?.reply || 'No response from server.',
						isUser: false,
						isLoading: false,
					};
					return newMessages;
				});
				setTopCandidates(data?.topCandidates || null);
				if (
					data?.topCandidates &&
					Array.isArray(data.topCandidates) &&
					data.topCandidates.length > 0
				) {
					addExtractedLocations(data.topCandidates, assistantMessageIndex);
					setIsRightOpen(true);
				}
				if (data?.sessionTitle) {
					setSessionList((prev) => {
						const sessionExists = prev.some(
							(session) => session.id === currentSessionId
						);
						if (sessionExists) {
							return prev.map((session) =>
								session.id === currentSessionId
									? { ...session, title: data.sessionTitle }
									: session
							);
						} else {
							return [
								{ id: currentSessionId, title: data.sessionTitle },
								...prev,
							];
						}
					});
				}
			} catch (fetchError) {
				clearTimeout(timeoutId);
				throw fetchError;
			}
		} catch (error) {
			let errorMessage = 'Failed to send message.';
			if (error instanceof Error) {
				if (error.name === 'AbortError') {
					errorMessage =
						'Request timed out after 2.5 minutes. The LLM engine is taking longer than expected to respond.';
				} else {
					errorMessage = error.message;
				}
			}
			setMessages((prev) => {
				const newMessages = [...prev];
				newMessages[newMessages.length - 1] = {
					text: errorMessage,
					isUser: false,
					isLoading: false,
				};
				return newMessages;
			});
		} finally {
			setIsSending(false);
		}
	};

	// Key down handler
	const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			if (chatState.newMessage.trim() && !chatState.isSending) {
				sendMessage(chatState.newMessage);
			}
		}
	};

	const actions = {
		sendMessage,
		startNewChat,
		loadOldMessages,
		handleDeleteSession,
		scrollToBottom,
		handleInputChange,
		handleKeyDown,
		toggleDropdown,
		handleGlobeTransition,
	};

	const contextValue: ChatContextValue = {
		chatState,
		uiState: enhancedUIState,
		locationState,
		actions,
		inputRef,
		messagesEndRef,
		messagesContainerRef,
		setChatState,
		setUIState,
		showLocationSetup,
		handleLocationSetupComplete,
	};

	return (
		<ChatContext.Provider value={contextValue}>{children}</ChatContext.Provider>
	);
}

// ===== MAIN EXPORT COMPONENT =====

const ChatPageContent: React.FC = () => {
	return (
		<ChatPageProvider>
			<ChatLayout />
		</ChatPageProvider>
	);
};

export default ChatPageContent;
